<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = buildRightAssignments(this.siteResourceID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));

		// build quick links -------------------------------------------------------------------------- ::
		this.link.message = buildCurrentLink(arguments.event,"message");
		this.link.list = buildCurrentLink(arguments.event,"list");
		this.link.edit = buildCurrentLink(arguments.event,"edit") & "&isflyout=1&mode=direct";
		this.link.exportCSV = buildCurrentLink(arguments.event,"exportCSV") & "&mode=stream";
		this.link.editInvoiceMessage = buildCurrentLink(arguments.event,"editInvoiceMessage") & "&mode=direct";
		this.link.confirmDeleteGLAccount = buildCurrentLink(arguments.event,"confirmDeleteGLAccount") & "&mode=direct";
		this.link.saveInvoiceMessage = buildCurrentLink(arguments.event,"saveInvoiceMessage") & "&mode=direct";
		this.link.viewGLAccountUsage = buildCurrentLink(arguments.event,"viewGLAccountUsage") & "&mode=direct";
		
		arguments.event.setValue('GLAAController',1);
		
		// method to run ------------------------------------------------------------------------------ ::
		local.methodToRun 	= this[arguments.event.getValue('mca_ta')];

		// pass the argument collection to the current method and execute it.
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="prepResult" type="string" required="false">

		<cfscript>
			var local = structNew();
			local.resultsList = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=GLAccountsJSON&meth=getAccountList&mode=stream';
			local.invoiceContentListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=GLAccountsJSON&meth=getInvoiceContentList&mode=stream';
			local.GLAccountsAuditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=GLAccountsJSON&meth=getGLAccountsAuditLog&mode=stream";
			local.showImpExTemplate = true;
			
			local.sampleImportTemplate = buildCurrentLink(arguments.event,"sampleImportTemplate") & "&mode=stream";
			local.exportGLAccountsStructureZIPLink = buildCurrentLink(arguments.event,"exportGLAccountsStructureZIP") & "&mode=stream";
			local.prepareGLAccountsImportLink = buildCurrentLink(arguments.event,"prepareGLAccountsImport");
			local.doImportGLAccountsLink = buildCurrentLink(arguments.event,"doImportGLAccounts") & "&mode=stream";
			local.massEditGLQBColsLink = buildCurrentLink(arguments.event,"massEditGLQBCols") & "&mode=direct";
		</cfscript>		

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.glManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfif arguments.event.getValue('tab','') eq 'import' and arguments.event.getValue('importFileName','') neq ''>
			<cfset local.showImpExTemplate = false>
			<cfset local.impExData = processGLAccountsImport(event=arguments.event)>		
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_list.cfm">
		</cfsavecontent>	

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="exportCSV" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.glManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "ChartOfAccounts.csv">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryStructure">
			set nocount on;
			
			IF OBJECT_ID('tempdb..##tmpGL') IS NOT NULL 
				DROP TABLE ##tmpGL;

			select gl.AccountType as [Account Type], 
				gl.AccountName as [Account Name], 	
				gl.ThePathExpanded as [Account Path], 
				gl.accountCode as [Account Code],
				ip.profileName as [Invoice Profile],  
				case gl.status when 'A' then 'Active' else 'Inactive' end as [Status], 
				case 
					when gl.accountTypeID <> 3 then '' 
					when gla.salesTaxProfileID is null then 'None' 
					else stp.profileName 
					end as [Sales Tax Profile],
				gl.acctSysName as [QuickBooks Online Account Name],
				gl.acctSysClass as [QuickBooks Online Class],
				<cfif arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
					gl2.accountName as [Deferred Account Name],
					gl2.accountCode as [Deferred Account Code],
				</cfif>
				invcon.contentTitle as [End-of-Invoice Content Title],
				invcon.rawContent as [End-of-Invoice Content],
				ROW_NUMBER() OVER(order by gl.thePath) as row
			INTO ##tmpGL
			FROM dbo.fn_getRecursiveGLAccountsWithAccountTypes(#arguments.event.getValue('mc_siteinfo.orgid')#) as gl
			INNER JOIN dbo.tr_glAccounts as gla on gla.glAccountID = gl.GLAccountID
			left outer join dbo.tr_InvoiceProfiles as ip on ip.profileID = gl.invoiceProfileID
			left outer join dbo.tr_salesTaxProfiles as stp on stp.profileID = gla.salesTaxProfileID
			outer apply dbo.fn_getContent(gla.invoiceContentID,1) as invcon
			<cfif arguments.event.getValue('mc_siteInfo.useAccrualAcct')>left outer join dbo.tr_GLAccounts as gl2 on gl2.glaccountID = gl.deferredGLAccountID</cfif>;

			DECLARE @selectsql varchar(max) = '
				SELECT [Account Type], [Account Name], [Account Path], [Account Code], [Invoice Profile], [Status], 
					[Sales Tax Profile], [QuickBooks Online Account Name], [QuickBooks Online Class],
					<cfif arguments.event.getValue("mc_siteInfo.useAccrualAcct")>[Deferred Account Name], [Deferred Account Code], </cfif> 
					[End-of-Invoice Content Title], [End-of-Invoice Content], row as mcCSVorder 
				*FROM* ##tmpGL';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpGL') IS NOT NULL 
				DROP TABLE ##tmpGL;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#" addtoken="no">
		</cfif>	
	</cffunction>

	<cffunction name="showSelector" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="appInstanceID" type="numeric" required="no" default="#this.appInstanceID#">

		<cfset var local = structNew()>

		<cfscript>
		// selectFN is required. If not passed in, use parent.closeBox
		if (NOT arguments.event.valueExists('selectFN') or len(arguments.event.getTrimValue('selectFN')) is 0)
			arguments.event.setValue('selectFN','parent.closeBox');
		arguments.event.paramValue('glatid',0);

		local.resultsList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=GLAccountsJSON&meth=getAccountListSelector&glatid=#arguments.event.getValue('glatid')#&mode=stream";
		local.urlString = "";

		// for the new account form
		local.objGL = CreateObject("component","model.admin.GLAccounts.GLAccounts");
		local.strGLAccounts = local.objGL.getGLAccounts(arguments.event.getValue('mc_siteInfo.orgID'));
		local.strAccount = local.objGL.getGLAccount(int(val(arguments.event.getValue('GLAccountID',0))),arguments.event.getValue('mc_siteInfo.orgID'));
		</cfscript>

		<!--- get the SRID and permissions of GLAccountsAdmin. This function is called directly from other applications and may not have the rights set. --->
		<cfset local.GLAccountsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='GLAccountsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsGLAccountsAdmin = buildRightAssignments(local.GLAccountsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_accountSelector.cfm">
		</cfsavecontent>	

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objAccount = CreateObject("component","GLAccounts")>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.glManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP&mode=direct" addtoken="no">
		</cfif>

		<!--- get account --->
		<cfset local.strAccount = local.objAccount.getGLAccount(int(val(arguments.event.getValue('GLAccountID',0))),arguments.event.getValue('mc_siteInfo.orgID'))>
		<cfset local.strGLAccounts = local.objAccount.getGLAccounts(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_editAccount.cfm">
		</cfsavecontent>				

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>	

	<cffunction name="editInvoiceMessage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.glManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP&mode=direct" addtoken="no">
		</cfif>

		<cfif int(val(arguments.event.getValue('cid',0)))>
			<cfset local.qryContent = CreateObject("component","GLAccounts").getInvoiceContentObjects(siteid=arguments.event.getValue('mc_siteInfo.siteid'), incContent=1, contentID=int(val(arguments.event.getValue('cid',0))))>
		<cfelse>
			<cfset local.qryContent = QueryNew("contentid,contenttitle,rawContent,isInUseIT,isInUseGL","integer,varchar,varchar,bit,bit")>
			<cfif queryAddRow(local.qryContent)>
				<cfset querySetCell(local.qryContent, "contentid", 0)>
				<cfset querySetCell(local.qryContent, "contenttitle", "")>
				<cfset querySetCell(local.qryContent, "rawContent", "")>
				<cfset querySetCell(local.qryContent, "isInUseIT", 0)>
				<cfset querySetCell(local.qryContent, "isInUseGL", 0)>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfinclude template="dsp_editInvoiceMessage.cfm">
			</cfoutput>
		</cfsavecontent>				

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>	

	<cffunction name="confirmDeleteGLAccount" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_confirmDeleteGLAccount.cfm">
		</cfsavecontent>				

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>	

	<cffunction name="saveInvoiceMessage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.glManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfset local.strSave = CreateObject("component","GLAccounts").saveInvoiceContent(siteID=arguments.event.getValue('mc_siteInfo.siteid'), 
			contentID=int(val(arguments.event.getValue('contentID',0))), contentTitle=arguments.event.getTrimValue('invoiceContentTitle',''),
			rawContent=arguments.event.getTrimValue('invoiceContent',''))>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif local.strSave.success>
				<div class="alert alert-success" role="alert">End-of-Invoice Message Saved Successfully</div>
				<script language="javascript">
					top.reloadInvoiceMessages();
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },2000);
				</script>
			<cfelse>
				<div class="alert alert-danger" role="alert">End-of-Invoice Message Could Not Be Saved</div>
				<script language="javascript">
					top.reloadInvoiceMessages();
					top.$("##btnMCModalSave").hide();
				</script>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewGLAccountUsage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.qryGLAccountUsage = CreateObject("component","GLAccounts").getGLAccountUsages(orgID=arguments.event.getValue('mc_siteinfo.orgID'),
			siteID=arguments.event.getValue('mc_siteinfo.siteID'), glAccountID=arguments.event.getValue('glid',0))>

		<cfset local.selectedTab = arguments.event.getTrimValue("tab","glAccountUsage")>
		<cfset local.lockTab = arguments.event.getTrimValue("lockTab","false") ? local.selectedTab : "">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_GLAccountUsage.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- export/import --->
	<cffunction name="exportGLAccountsStructureZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "ChartOfAccountsStructure.zip">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_exportGLAccountsStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />

		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="false">
		</cfif>
	</cffunction>

	<cffunction name="prepareGLAccountsImport" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>
		
		<cfsetting requesttimeout="500">

		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">
			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>
			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/ChartOfAccountsStructure.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/ChartOfAccountsStructure.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>
		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/ChartOfAccountsStructure.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name in ('sync_tr_GLAccounts.bcp','sync_tr_gl_content.bcp','sync_tr_gl_supporting.bcp')
				</cfquery>
				<cfif local.qryFiles.recordcount neq 3>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain five.">
				<cfelseif local.qryFilesCheck.theCount neq 3>
					<cfthrow message="Required files in the backup file is missing.">
				</cfif>
				<cfzip file="#local.strImportFile.strFolder.folderPath#/ChartOfAccountsStructure.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/ChartOfAccountsStructure.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>

  		<!--- prepare import --->
  		<cfif local.rs.success>

			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Chart Of Accounts Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>

			<cfset local.GLAccountsImportStruct = application.mcCacheManager.sessionGetValue(keyname='GLAccountsImportStruct', defaultValue=structNew())>
			<cfset structInsert(local.GLAccountsImportStruct, local.threadID, local.strImportFile.strFolder, true)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='GLAccountsImportStruct', value=local.GLAccountsImportStruct)>
			
			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareGLAccountsImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>
			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.prepResult">
				<cfoutput>
				<div id="loadingGif" class="row mt-2">
					<div class="col-auto">
						<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> 
					</div>
					<div class="col">
						<div class="pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.prepResult">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.list#&tab=ex';">Try Again</button> 
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfset local.data = list(event=arguments.event, prepResult=local.prepResult)>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareGLAccountsImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.siteID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.tr_prepareGLAccountsImport @siteID=@siteID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					set @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showGLAccountsImportCompareResults(orgID=arguments.paramStruct.orgID, threadID=arguments.paramStruct.threadID, strResult=local.rs, doAgainURL="#this.link.list#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/GLAccountsImportReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="showGLAccountsImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>
		
		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewContents = XMLSearch(arguments.strResult.importResultXML,"/import/newcontent/content")>
			<cfset local.strImportResult.arrUpdateContents = XMLSearch(arguments.strResult.importResultXML,"/import/updatecontent/content")>
			<cfset local.strImportResult.arrRemoveContents = XMLSearch(arguments.strResult.importResultXML,"/import/removecontent/content")>
			<cfset local.strImportResult.arrUnableToRemoveContents = XMLSearch(arguments.strResult.importResultXML,"/import/unabletoremovecontent/content")>
			<cfset local.strImportResult.arrNewGLAccounts = XMLSearch(arguments.strResult.importResultXML,"/import/newglaccounts/account")>
			<cfset local.strImportResult.arrUpdateGLAccounts = XMLSearch(arguments.strResult.importResultXML,"/import/updateglaccounts/account")>
			<cfset local.strImportResult.arrRemoveGLAccounts = XMLSearch(arguments.strResult.importResultXML,"/import/removeglaccounts/account")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfif local.hasChanges>
				<cfset local.importReport = generateGLAccountsImportResultsReport(orgID=arguments.orgID, threadID=arguments.threadID, strImportResult=local.strImportResult)>
			</cfif>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
			<cfset local.errorReport = generateGLAccountsImportErrorReport(orgID=arguments.orgID, arrErrors=local.arrErrors)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Chart of Accounts Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div class="font-weight-bold">An undetermined error occurred during the import.</div>
										</cfif>
									<cfelse>
										<div class="font-weight-bold">The import was stopped and requires your attention.</div>
										<div class="mt-2">#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelGLAccountsImport(orgID=arguments.orgID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Chart of Accounts Import No Action Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div>There were no changes to process.</div>
								<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importReport)>
					<div>#local.importReport#</div>
					<br/>
				</cfif>
				<br/>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Chart of Accounts Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger font-weight-bold">
									An undetermined error occurred during the import.
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateGLAccountsImportResultsReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateGLAccounts)>
			<cfquery name="local.qryImportFileUpdateGLAccounts" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select sgl.GLAccountID as syncGLAccountsID, sgl.[uid], sgl.AccountName, sgl.AccountCode, spgl.[uid] as parentGLAccountUID, 
					spgl.thePathExpanded as parentGLAccountPathExpanded, sgl.[status], sgl.GLCode, sgl_ip.itemType as invoiceProfileName, 
					isnull(sgl_c.contentTitle,'') as contentTitle, sdgl.[uid] as deferredGLAccountUID, sdgl.accountName as deferredAccountName, 
					sdgl.thePathExpanded as deferredGLAccountPathExpanded, 
					sgl_stxp.itemType as salesTaxProfileName, sgl_stxpcat.itemType as salesTaxTaxJarCategoryName, 
					sgl.acctSysName, sgl.acctSysClass, sgl.thePathExpanded
				from dbo.sync_tr_GLAccounts as sgl
				left outer join dbo.sync_tr_GLAccounts as spgl on spgl.orgID = @orgID and spgl.GLAccountID = sgl.parentGLAccountID
				left outer join dbo.sync_tr_GLAccounts as sdgl on sdgl.orgID = @orgID and sdgl.GLAccountID = sgl.deferredGLAccountID
				left outer join dbo.sync_tr_gl_supporting as sgl_ip on sgl_ip.orgID = @orgID and sgl_ip.cat = 'acctip' and sgl_ip.itemID = sgl.invoiceProfileID
				left outer join dbo.sync_tr_gl_content as sgl_c on sgl_c.orgID = @orgID and sgl_c.contentID = sgl.invoiceContentID
				left outer join dbo.sync_tr_gl_supporting as sgl_stxp on sgl_stxp.orgID = @orgID and sgl_stxp.cat = 'salestxp' and sgl_stxp.itemID = sgl.salesTaxProfileID
				left outer join dbo.sync_tr_gl_supporting as sgl_stxpcat on sgl_stxpcat.orgID = @orgID and sgl_stxpcat.cat = 'taxjarcat' and sgl_stxpcat.itemID = sgl.salesTaxTaxJarCategoryID
				where sgl.orgID = @orgID
				and sgl.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateGLAccounts" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select account.glaccountid, account.accountname, account.accountcode, account.glcode, account.status, 
					isnull(ip.profileName,'') as invoiceProfileName, isnull(account.deferredglaccountid,0) as deferredglaccountid, 
					isnull(gl.acctSysName,'') as acctSysName, isnull(gl.acctSysClass,'') as acctSysClass, 
					account.thePathExpanded, invoiceContent.contenttitle, invoiceContent.rawcontent,
					glD.uid as deferredUID, glD.accountName as deferredAccountName, stp.profileName as salesTaxProfileName, 
					stpcat.category as salesTaxTaxJarCategoryName, gl.uid
				from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as account
				inner join dbo.tr_GlAccounts as gl on gl.glAccountID = account.glAccountID
				left outer join dbo.tr_invoiceProfiles as ip on ip.profileID = gl.invoiceprofileid
				left outer join dbo.tr_GLAccounts as glD on glD.glAccountID = gl.deferredGLAccountID
				left outer join dbo.tr_salesTaxProfiles as stp on stp.profileID = gl.salesTaxProfileID
				left outer join dbo.tr_salesTaxCategories_TaxJar as stpcat on stpcat.categoryID = gl.salesTaxTaxJarCategoryID
				outer apply dbo.fn_getContent(gl.invoiceContentID,1) as invoiceContent
				where gl.orgID = @orgID
				and account.status <> 'D'
				and (account.isSystemAccount = 0 OR (account.accounttypeid = 5 AND isnull(account.glcode,'') <> 'DEPOSITS'))
				and gl.[uid] in (#listQualify(valueList(local.qryImportFileUpdateGLAccounts.uid), "'")#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>		

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompare.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateGLAccountsImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="arrErrors" type="array" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doImportGLAccounts" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">
		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>

		<cfsetting requesttimeout="500">

		<cfset local.GLAccountsImportStruct = application.mcCacheManager.sessionGetValue(keyname='GLAccountsImportStruct', defaultValue=structNew())>
		<cfif NOT structKeyExists(local.GLAccountsImportStruct, local.threadID)>
			<cfset local.resultMessage = "There was a problem importing the Chart of Accounts. The import data is no longer available.">
		<cfelse>
			<cftry>
				<cfstoredproc procedure="tr_importAllGLAccounts" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Chart of Accounts file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>

			<!--- when done, remove from struct --->
			<cfset StructDelete(local.GLAccountsImportStruct, local.threadID)>
			<cfif structCount(local.GLAccountsImportStruct)>
				<cfset application.mcCacheManager.sessionSetValue(keyname='GLAccountsImportStruct', value=local.GLAccountsImportStruct)>
			<cfelse>
				<cfset application.mcCacheManager.sessionDeleteValue(keyname='GLAccountsImportStruct')>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelGLAccountsImport" access="private" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				
				BEGIN TRAN;
					DELETE FROM dbo.sync_tr_GLAccounts WHERE orgID = @orgID;
					DELETE FROM dbo.sync_tr_gl_content WHERE orgID = @orgID;
					DELETE FROM dbo.sync_tr_gl_supporting WHERE orgID = @orgID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="fetchReportData" access="public" output="false" returntype="struct">
		<cfargument name="reportuid" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		<cfset local.GLAccountsImportStruct = application.mcCacheManager.sessionGetValue(keyname='GLAccountsImportStruct', defaultValue=structNew())>

		<cftry>
			<cfif structKeyExists(local.GLAccountsImportStruct, arguments.reportuid)>
				<cfset local.reportFileName = local.GLAccountsImportStruct[arguments.reportuid].folderPath & "/GLAccountsImportReport.html">
				<cfset local.returnStruct.reportOutput = "">
				<cfif fileExists(local.reportFileName)>
					<cffile action="read" file="#local.reportFileName#" variable="local.returnStruct.reportOutput">
					<cfset local.returnStruct.success = true>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>
					
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry!</h4>
				<div>
					<cfswitch expression="#arguments.event.getValue('ec','')#">
						<cfcase value="LNP">You do not have the necessary permissions to manage GL Accounts.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfdefaultcase>An error has occurred. Contact MemberCentral for assistance.</cfdefaultcase>
					</cfswitch>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="sampleImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","GLAccountsImport").generateImportTemplate()>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="processGLAccountsImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","GLAccountsImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importChartOfAccounts(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=this.link.list)>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="listLimits" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.limitScheduleListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=GLAccountsJSON&meth=getLimitScheduleList&mode=stream';
			local.editListLimitScheduleLink =  buildCurrentLink(arguments.event,"editListLimitSchedule") & "&mode=direct";
		</cfscript>
		<cfset arguments.event.removeValue('GLAAController')>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.glManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_listLimits.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editListLimitSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objGL = CreateObject("component","GLAccounts")>
		<cfset local.scheduleID = arguments.event.getValue('scheduleID',0)>
		<cfset local.qryLimitSchedule = local.objGL.getLimitSchedule(orgID=arguments.event.getValue('mc_siteInfo.orgid'), scheduleID=local.scheduleID)>
		<cfset local.editGLAccountLimitLink = buildCurrentLink(arguments.event,"editGLAccountLimit") & "&mode=stream">// set account type to revenue
		<cfset arguments.event.removeValue('GLAAController')>
		<cfset arguments.event.setValue('glatid',3)>
		<cfset arguments.event.setValue('selectFN','selectGLAccountResult')>
		<cfset local.showGLSelector = CreateObject("component","model.admin.GLAccounts.GLAccountsAdmin").showSelector(event=arguments.event, appInstanceID=this.appInstanceID)>

		<cfif local.scheduleID gt 0>
			<cfset local.limitGLAcctListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=GLAccountsJSON&meth=getLimitGLAccount&scheduleID=#local.scheduleID#&mode=stream">	
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editLimitSchedule.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editGLAccountLimit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objGL = CreateObject("component","GLAccounts")>
		<cfset local.qryGLAccountLimit = local.objGL.getGLAccountLimit(orgID=arguments.event.getValue('mc_siteInfo.orgid'), scheduleID=arguments.event.getValue('scheduleID',0), limitID=arguments.event.getValue('limitID',0))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editGLAccountLimit.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEditGLQBCols" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.glManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfset local.GLListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=GLAccountsJSON&meth=getAccountList&mode=stream'>
		<cfset local.formLink = buildCurrentLink(arguments.event,"massUpdateGLQuickBookMappings") & "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_massEditGLQBCols.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massUpdateGLQuickBookMappings" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.glManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfset local.strQBData = deSerializeJSON(arguments.event.getTrimValue('QBData'))>
		<cfset local.arrGLQBMappings = []>

		<cfquery name="local.qryGLs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT GLAccountID, accountName, ISNULL(acctSysName,'') AS acctSysName, ISNULL(acctSysClass,'') AS acctSysClass
			FROM dbo.tr_GLAccounts
			WHERE orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
			AND GLAccountID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.strQBData.glIDList#">)
			AND [status] <> 'D';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryGLs">
			<cfset local.acctSysName = replace(local.strQBData.strQBCols[local.qryGLs.GLAccountID].acctSysName,'"','','all')>
			<cfset local.acctSysClass = local.strQBData.strQBCols[local.qryGLs.GLAccountID].acctSysClass>
			<cfset local.arrChangeMsg = []>

			<cfif compare(local.acctSysName,local.qryGLs.acctSysName)>
				<cfset local.arrChangeMsg.append("GL Account [#local.qryGLs.accountName#] QuickBooks Account Name changed from [#len(local.qryGLs.acctSysName) ? local.qryGLs.acctSysName : 'blank'#] to [#len(local.acctSysName) ? local.acctSysName : 'blank'#].")>
			</cfif>
			<cfif compare(local.acctSysClass,local.qryGLs.acctSysClass)>
				<cfset local.arrChangeMsg.append("GL Account [#local.qryGLs.accountName#] QuickBooks Class changed from [#len(local.qryGLs.acctSysClass) ? local.qryGLs.acctSysClass : 'blank'#] to [#len(local.acctSysClass) ? local.acctSysClass : 'blank'#].")>
			</cfif>

			<cfif arrayLen(local.arrChangeMsg)>
				<cfset local.arrGLQBMappings.append({
					"glID": local.qryGLs.GLAccountID,
					"acctSysName": local.acctSysName,
					"acctSysClass": local.acctSysClass,
					"changeMsg": arrayToList(local.arrChangeMsg,' ')
				})>
			</cfif>
		</cfloop>

		<cfif arrayLen(local.arrGLQBMappings)>
			<cfset CreateObject("component","GLAccounts").massUpdateGLAccountQuickBookMappings(orgID=arguments.event.getValue('mc_siteInfo.orgid'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), arrGLQBMappings=local.arrGLQBMappings)>
		</cfif>

		<cfreturn returnAppStruct(serializeJSON({"success":true}),"echo")>
	</cffunction>

</cfcomponent>