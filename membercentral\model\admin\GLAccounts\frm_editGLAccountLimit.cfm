<cfsavecontent variable="local.accountLimitJS">
	<cfoutput>
	<script language="javascript">
		function saveGLAccountLimit() {
			mca_hideAlert('err_accountlimit');

			if($('##limitGLAccountID').val() == 0) {
				mca_showAlert('err_accountlimit', 'Enter the limit GL Account.');
				return false;
			}

			var maxAmount = Number($('##maxAmount').val().replace(/[^0-9\.]+/g,""));

			var saveResult = function(r) {
				$('##btnSaveLimitGL').html('Save').attr('disabled',false);
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadPage();
					$('div##addGLAccountLimit').html('').hide();
				} else if (r.success && r.success.toLowerCase() == 'false') {
					var jserr = '';
					var emsg = $.parseXML(r.xmlvalidationresult);
					$(emsg).find('e').each(function() {
						jserr += $(this).attr('msg') + '<br/>';
					});
					mca_showAlert('err_accountlimit', jserr);
				} else {
					alert('We ran into an error while saving the limit GL Account. Try again.');
				}
			};

			$('##btnSaveLimitGL').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Saving...').attr('disabled',true);

			var objParams = { scheduleID:$('##limitScheduleID').val(), limitID:$('##limitID').val(), glAccountID:$('##limitGLAccountID').val(), maxAmount:maxAmount };
			TS_AJX('ADMINGLACCT','saveGLAccountLimit',objParams,saveResult,saveResult,10000,saveResult);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.accountLimitJS)#">

<cfoutput>
<form name="frmGLAccountLimit" id="frmGLAccountLimit">
<input type="hidden" name="limitID" id="limitID" value="#val(local.qryGLAccountLimit.limitID)#">
<input type="hidden" name="limitScheduleID" id="limitScheduleID" value="#arguments.event.getValue('scheduleID',0)#">
<div class="card card-box my-2">
	<div class="card-header py-1 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Add Limit</div>
	</div>
	<div class="card-body pb-3">
		<div id="err_accountlimit" class="alert alert-danger mb-2 d-none"></div>
		<div id="divlimitGLerr" class="alert alert-danger" style="display:none;"></div>
		
		<div class="form-group mb-3">
			<div class="form-label-group">
				<input type="hidden" name="limitGLAccountID" id="limitGLAccountID" value="#val(local.qryGLAccountLimit.glAccountID)#">
				<input type="text" id="limitGLAccountPath" value="<cfif len(local.qryGLAccountLimit.GLAccountPath)>#local.qryGLAccountLimit.GLAccountPath#<cfelse>(No account selected)</cfif>" class="form-control" maxlength="20" readonly disabled="true">
				<label for="limitGLAccountPath">Revenue GL <span class="text-danger">*</span></label>
				<a href="javascript:void(0)" class="selectAccount" selectedAcc="0" type="limit">Choose GL Account</a><span id="limitglSaveMsg" class="font-weight-bold text-danger ml-3"></span>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<div class="input-group">
					<input type="text" name="maxAmount" id="maxAmount" value="#local.qryGLAccountLimit.maxAmount#" class="form-control">
					<div class="input-group-append">
						<span class="input-group-text" data-target="maxAmount">$</span>
					</div>
					<label for="maxAmount">Maximum Amount</label>
				</div>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col-sm-12">
				<button type="button" id="btnSaveLimitGL" name="btnSaveLimitGL" class="btn btn-sm btn-primary" onclick="saveGLAccountLimit();"><cfif arguments.event.getValue('limitID',0) is 0>Add<cfelse>Update</cfif></button>
			</div>
		</div>
	</div>
</div>
</form>
</cfoutput>