ALTER PROC dbo.tr_getScheduledPaymentsForMember
@orgID int,
@siteID int,
@memberID int

AS


SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @nowDate date = GETDATE();
	
	IF OBJECT_ID('tempdb..#tmpInvoiceProfileProcFeeOverrides') IS NOT NULL
		DROP TABLE #tmpInvoiceProfileProcFeeOverrides;
	CREATE TABLE #tmpInvoiceProfileProcFeeOverrides (autoID int IDENTITY(1,1) PRIMARY KEY, refID int, enableProcessingFeeDonation bit,
		processFeeDonationFETitle varchar(100), processFeeDonationFEMsg varchar(800), processFeeDonationDefaultSelect bit);

	/* subscriptions */
	IF OBJECT_ID('tempdb..#tblSubscription') IS NOT NULL
		DROP TABLE #tblSubscription;
	IF OBJECT_ID('tempdb..#tblSubscriptionRate') IS NOT NULL
		DROP TABLE #tblSubscriptionRate;
	IF OBJECT_ID('tempdb..#tmpSubs') IS NOT NULL
		DROP TABLE #tmpSubs;
	IF OBJECT_ID('tempdb..#tmpSubsExistingInvoices') IS NOT NULL
		DROP TABLE #tmpSubsExistingInvoices;
	IF OBJECT_ID('tempdb..#mcSubscribersForAcct') IS NOT NULL
		DROP TABLE #mcSubscribersForAcct;
	IF OBJECT_ID('tempdb..#mcSubscriberTransactions') IS NOT NULL
		DROP TABLE #mcSubscriberTransactions;
	CREATE TABLE #tblSubscription (subscriptionID int PRIMARY KEY, typeID int, subscriptionName varchar(300));
	CREATE TABLE #tblSubscriptionRate (rateID int PRIMARY KEY, rateName varchar(200), canAssocPayMethod bit, eligibleProfileIDList varchar(max));
	CREATE TABLE #tmpSubs (subscriberID int PRIMARY KEY, subscriptionID int, typeName varchar(100), subscriptionName varchar(300),
		[status] varchar(1), statusName varchar(50), subStartDate datetime, subEndDate datetime, rootSubscriberID int, payProfileID int,
		payProcessFee bit, processFeePercent decimal(5, 2), rateName varchar(200), frequencyName varchar(50), billAmt1 decimal(18,2), billedAmt decimal(18,2), dueAmt decimal(18,2),
		nextInvDueDate date, nextInvDueAmt decimal(18,2), overDueInvDueDate date, overDueInvDueAmt decimal(18,2), earliestOverdueInvoiceDate date, totalOverdueAmt decimal(18,2),
		eligibleProfileIDList varchar(max), ovEnableProcessingFeeDonation bit, ovProcessFeeDonationFETitle varchar(100), ovProcessFeeDonationFEMsg varchar(800), ovProcessFeeDonationDefaultSelect bit);
	CREATE TABLE #mcSubscribersForAcct (subscriberID int PRIMARY KEY);
	CREATE TABLE #mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
		invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
		amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime,
		assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
		creditGLAccountID int, INDEX IDX_mcSubscriberTransactions_subscriberID_transactionID (subscriberID, transactionID));

	-- eligible rates
	INSERT INTO #tblSubscriptionRate (rateID, rateName, canAssocPayMethod, eligibleProfileIDList)
	SELECT r.rateID, r.rateName, CASE WHEN COUNT(rfmp.profileID) > 0 THEN 1 ELSE 0 END,
		STRING_AGG(rfmp.profileID, ',')
	FROM dbo.sub_rates AS r
	INNER JOIN dbo.sub_rateSchedules AS rs ON rs.scheduleID = r.scheduleID
		AND rs.siteID = @siteID
	INNER JOIN dbo.sub_rateFrequencies AS rf ON rf.rateID = r.rateID
	LEFT OUTER JOIN dbo.sub_rateFrequenciesMerchantProfiles AS rfmp
		INNER JOIN dbo.mp_profiles AS p ON p.siteID = @siteID
			AND p.profileID = rfmp.profileID
			AND p.[status] IN ('A','I')
		INNER JOIN mp_gateways AS g ON g.gatewayID = p.gatewayID
			AND g.gatewayClass IN ('creditcard','bankdraft')
			AND g.isActive = 1
		ON rfmp.rfid = rf.rfid
		AND rfmp.[status] = 'A'
	GROUP BY r.rateID, r.rateName;

	INSERT INTO #tmpSubs (subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName,
		subStartDate, subEndDate, rootSubscriberID, payProfileID, payProcessFee, processFeePercent,
		rateName, billedAmt, dueAmt, eligibleProfileIDList)
	SELECT subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName,
		subStartDate, subEndDate, rootSubscriberID, payProfileID, payProcessFee, processFeePercent, rateName,
		CAST(0 as decimal(18,2)) as billedAmt, CAST(0 as decimal(18,2)) as dueAmt, eligibleProfileIDList
	FROM (
		SELECT s.subscriberID, s.subscriptionID, t.typeName,
			sub.subscriptionName, rst.statusCode as [status], rst.statusName,
			s.subStartDate, s.subEndDate, s.rootSubscriberID, s.payProfileID, s.payProcessFee, s.processFeePercent,
			r.ratename, r.eligibleProfileIDList
		FROM dbo.sub_subscribers as s
		INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.activeMemberID = @memberID and s.memberID = m.memberID 
		INNER JOIN dbo.sub_rateFrequencies as rf on rf.rfid = s.rfid
		INNER JOIN dbo.sub_statuses AS rst ON rst.statusID = s.statusID
			AND rst.statusCode in ('P','A','O','R','I')
		INNER JOIN dbo.sub_subscriptions AS sub ON sub.subscriptionID = s.subscriptionID
			AND sub.orgID = @orgID
		INNER JOIN dbo.sub_Types AS t ON t.typeID = sub.typeID
			AND t.siteID = @siteID
		INNER JOIN #tblSubscriptionRate as r on r.rateID = rf.rateID
		WHERE s.orgID = @orgID
		AND (s.payProfileID IS NOT NULL OR r.canAssocPayMethod = 1)
	) as x;

	-- get bill amounts for subs in R or O status
	update tmpSubs set 
		billAmt1 = updates.billAmt
	from #tmpsubs tmpsubs 
	inner join (
		select [uncommitted].rootSubscriberID, sum([uncommitted].billAmt) as billAmt
		from (
			select tmp.rootSubscriberID, 
				billAmt = case 
					when s1.PCFree = 1 then 0.00
					when s1.modifiedRate is not null then s1.modifiedRate + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(s1.glAccountID,s1.modifiedRate,getdate(),ma1.stateID)),0)
					else s1.lastPrice + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(s1.glAccountID,s1.lastPrice,getdate(),ma1.stateID)),0)
					end
			from #tmpSubs as tmp
			inner join dbo.sub_subscribers as s1 
				on s1.orgID=@orgID
				and s1.rootSubscriberID = tmp.rootSubscriberID
				and tmp.rootSubscriberID=tmp.subscriberID
			INNER JOIN dbo.sub_statuses AS st ON st.statusID = s1.statusID
				AND st.statusCode in ('R','O')
			inner join dbo.ams_members as m1 
				on m1.orgID = @orgID
				and m1.memberID = s1.memberID
			left outer join dbo.ams_memberAddresses as ma1
				inner join dbo.ams_memberAddressTags as matag1 on matag1.orgID = @orgID 
					and matag1.memberID = ma1.memberID
					and matag1.addressTypeID = ma1.addressTypeID
				inner join dbo.ams_memberAddressTagTypes as matagt1 on matagt1.orgID = @orgID
					and matagt1.addressTagTypeID = matag1.addressTagTypeID
					and matagt1.addressTagType = 'Billing'
				on ma1.orgID = @orgID
				and ma1.memberID = m1.activeMemberID
		) as [uncommitted]
		group by [uncommitted].rootSubscriberID
	) as updates on updates.rootSubscriberID = tmpsubs.rootSubscriberID and tmpsubs.[status] in ('R','O');

	-- get existing invoices and populate #mcSubscriberTransactions
	INSERT INTO #mcSubscribersForAcct (subscriberID)
	SELECT distinct subscriberID
	FROM #tmpSubs;

	EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	select st.invoiceID, tmp.subscriberID, tmp.rootSubscriberID, st.amountDue as totalRemaining
	into #tmpSubsExistingInvoices
	from #tmpSubs as tmp
	inner join #mcSubscriberTransactions as st on st.subscriberID = tmp.subscriberID;

	-- R and O have no transactions
	update #tmpSubs
	set billedAmt = billAmt1,
		dueAmt = billAmt1
	where subscriberID = rootSubscriberID
	AND [status] in ('R','O');

	-- if there are NOT any invoices for the sub tree
	update tmp
	set tmp.billedAmt = tmpBilling.billAmt,
		tmp.dueAmt = 0
	from #tmpSubs as tmp
	outer apply (
		select sum(priceToUse) as billAmt
		from (
			select case 
				when s1.PCFree = 1 then 0.00
				when s1.modifiedRate is not null then s1.modifiedRate + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(s1.glAccountID,s1.modifiedRate,getdate(),ma1.stateID)),0)
				else s1.lastPrice + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(s1.glAccountID,s1.lastPrice,getdate(),ma1.stateID)),0)
				end as priceToUse
			from dbo.sub_subscribers s1 
			inner join dbo.sub_statuses st on st.statusID = s1.statusID and st.statusCode <> 'D'
			inner join dbo.ams_members as m1 on m1.orgID = @orgID and m1.memberID = s1.memberID
			left outer join dbo.ams_memberAddresses as ma1
				inner join dbo.ams_memberAddressTags as matag1 on matag1.orgID = @orgID 
					and matag1.memberID = ma1.memberID
					and matag1.addressTypeID = ma1.addressTypeID
				inner join dbo.ams_memberAddressTagTypes as matagt1 on matagt1.orgID = @orgID
					and matagt1.addressTagTypeID = matag1.addressTagTypeID
					and matagt1.addressTagType = 'Billing'
				on ma1.orgID = @orgID
				and ma1.memberID = m1.activeMemberID
			where s1.orgID = @orgID
			and s1.rootSubscriberID = tmp.rootSubscriberID
		) as innerSales
	) as tmpBilling
	where tmp.subscriberID = tmp.rootSubscriberID
	and tmp.[status] not in ('R','O')
	and NOT EXISTS (select 1 from #tmpSubsExistingInvoices where rootSubscriberID = tmp.rootSubscriberID);

	-- if there are any invoices for the sub tree
	update tmp
	set tmp.billedAmt = tmpBilling.billAmt,
		tmp.dueAmt = tmpBilling.dueAmt
	from #tmpSubs as tmp
	outer apply (
		select billAmt = sum(tsFull.cache_amountAfterAdjustment), dueAmt = sum(tsFull.cache_amountAfterAdjustment - tsFull.cache_activePaymentAllocatedAmount)
		from (
			select distinct tmp2.rootSubscriberID, st.transactionID
			from #tmpSubs as tmp2
			inner join #mcSubscriberTransactions as st on st.subscriberID = tmp2.subscriberID
		) as tmp3
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,tmp3.transactionID) as tsFull
		where tmp3.rootSubscriberID = tmp.rootSubscriberID
	) as tmpBilling
	where tmp.subscriberID = tmp.rootSubscriberID
	and tmp.[status] not in ('R','O')
	and EXISTS (select 1 from #tmpSubsExistingInvoices where rootSubscriberID = tmp.rootSubscriberID);

	-- only keep root subscriber entries once calculations are done
	DELETE FROM #tmpSubs WHERE subscriberID <> rootSubscriberID;

	-- only entries with subscribers in the tree have a total price greater than zero
	DELETE FROM #tmpSubs WHERE billedAmt = 0;

	-- get the next invoice due date/amount and first overdue invoice date/amount (if present)
	UPDATE tmp
	SET tmp.nextInvDueDate = dueInv.dateDue,
		tmp.nextInvDueAmt = dueInv.amountDue,
		tmp.overDueInvDueDate = overDueInv.dateDue,
		tmp.overDueInvDueAmt = overDueInv.amountDue,
		tmp.totalOverdueAmt = totalOverDue.amountDue,
		tmp.earliestOverdueInvoiceDate = totalOverDue.earliestOverdueInvoiceDate
	FROM #tmpSubs as tmp
	LEFT OUTER JOIN (
		SELECT tmp.subscriberID, i.dateDue, sum(tmp.totalRemaining) AS amountDue,
			ROW_NUMBER() OVER (PARTITION BY tmp.subscriberID ORDER BY i.dateDue ASC) AS rowNum
		FROM #tmpSubsExistingInvoices AS tmp
		INNER JOIN dbo.tr_invoices AS i ON i.invoiceID = tmp.invoiceID
		WHERE tmp.totalRemaining > 0
		AND i.dateDue >= @nowDate
		group by tmp.subscriberID, i.dateDue
	) AS dueInv ON dueInv.subscriberID = tmp.subscriberID AND dueInv.rowNum = 1
	LEFT OUTER JOIN (
		SELECT tmp.rootsubscriberID, i.dateDue, sum(tmp.totalRemaining) AS amountDue,
			ROW_NUMBER() OVER (PARTITION BY tmp.rootsubscriberID ORDER BY i.dateDue ASC) AS rowNum
		FROM #tmpSubsExistingInvoices AS tmp
		INNER JOIN dbo.tr_invoices AS i ON i.invoiceID = tmp.invoiceID
		WHERE tmp.totalRemaining > 0
		AND i.dateDue < @nowDate
		group by tmp.rootsubscriberID, i.dateDue
	) AS overDueInv ON overDueInv.rootsubscriberID = tmp.subscriberID AND overDueInv.rowNum = 1
	LEFT OUTER JOIN (
		SELECT tmp.rootsubscriberID, sum(tmp.totalRemaining) AS amountDue, min(i.dateDue) as earliestOverdueInvoiceDate 
		FROM #tmpSubsExistingInvoices AS tmp
		INNER JOIN dbo.tr_invoices AS i ON i.invoiceID = tmp.invoiceID
		WHERE tmp.totalRemaining > 0
		AND i.dateDue < @nowDate
		group by tmp.rootsubscriberID
	) AS totalOverDue ON totalOverDue.rootsubscriberID = tmp.subscriberID;

	WITH selectedForFeeOverride AS (
		SELECT subscriberID, MAX(invoiceProfileID) AS invoiceProfileID
		FROM #mcSubscriberTransactions
		GROUP BY subscriberID
		HAVING COUNT(DISTINCT invoiceProfileID) = 1
	)
	INSERT INTO #tmpInvoiceProfileProcFeeOverrides (refID, enableProcessingFeeDonation, processFeeDonationFETitle, processFeeDonationFEMsg, processFeeDonationDefaultSelect)
	SELECT tmp.subscriberID, ip.enableProcessingFeeDonation, pfm.title, pfm.[message], ip.processFeeDonationDefaultSelect
	FROM #tmpSubs AS tmp
	INNER JOIN selectedForFeeOverride AS sel ON sel.subscriberID = tmp.subscriberID
	INNER JOIN dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID
		AND ip.profileID = sel.invoiceProfileID
	LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = ip.solicitationMessageID;

	UPDATE tmp
	SET tmp.ovEnableProcessingFeeDonation = tmpOv.enableProcessingFeeDonation,
		tmp.ovProcessFeeDonationFETitle = tmpOv.processFeeDonationFETitle,
		tmp.ovProcessFeeDonationFEMsg = tmpOv.processFeeDonationFEMsg,
		tmp.ovProcessFeeDonationDefaultSelect = tmpOv.processFeeDonationDefaultSelect
	FROM #tmpSubs as tmp
	LEFT OUTER JOIN #tmpInvoiceProfileProcFeeOverrides AS tmpOv ON tmpOv.refID = tmp.subscriberID;

	TRUNCATE TABLE #tmpInvoiceProfileProcFeeOverrides;

	IF OBJECT_ID('tempdb..#tblSubscriptionRate') IS NOT NULL
		DROP TABLE #tblSubscriptionRate;
	IF OBJECT_ID('tempdb..#tmpSubsExistingInvoices') IS NOT NULL
		DROP TABLE #tmpSubsExistingInvoices;
	IF OBJECT_ID('tempdb..#mcSubscribersForAcct') IS NOT NULL
		DROP TABLE #mcSubscribersForAcct;
	IF OBJECT_ID('tempdb..#mcSubscriberTransactions') IS NOT NULL
		DROP TABLE #mcSubscriberTransactions;

	/* contributions */
	DECLARE @contributionIDList varchar(max);

	IF OBJECT_ID('tempdb..#tmpContributions') IS NOT NULL
		DROP TABLE #tmpContributions;
	IF OBJECT_ID('tempdb..#tmpContributionsPledged') IS NOT NULL
		DROP TABLE #tmpContributionsPledged;
	IF OBJECT_ID('tempdb..#tmpContribInvoices') IS NOT NULL
		DROP TABLE #tmpContribInvoices;
	IF OBJECT_ID('tempdb..#tmpContribInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpContribInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpContribProgramPayProfiles') IS NOT NULL
		DROP TABLE #tmpContribProgramPayProfiles;
	IF OBJECT_ID('tempdb..#tmpContribProgramPayProfileIDs') IS NOT NULL
		DROP TABLE #tmpContribProgramPayProfileIDs;
	CREATE TABLE #tmpContributions (programID int, programName varchar(200), rateName varchar(200), frequency varchar(20), contributionID int PRIMARY KEY, 
		isPerpetual bit, startdate date, endDate date, statusName varchar(30), statusCode char(1), totalPledge decimal(18,2),
		payProfileID int, payProcessFee bit, processFeePercent decimal(5, 2),
		firstPaymentDate date, totalPledgeFirst decimal(18,2), totalPledgeRecurring decimal(18,2),
		nextInvDueDate date, nextInvDueAmt decimal(18,2), nextInstallmentDueDate date, nextInstallmentDueAmt decimal(18,2),
		overDueInvDueDate date, overDueInvDueAmt decimal(18,2), earliestOverdueInvoiceDate date, totalOverdueAmt decimal(18,2), eligibleProfileIDList varchar(max),
		ovEnableProcessingFeeDonation bit, ovProcessFeeDonationFETitle varchar(100), ovProcessFeeDonationFEMsg varchar(800), ovProcessFeeDonationDefaultSelect bit);
	CREATE TABLE #tmpContributionsPledged (contributionID int, totalPledgeFirst decimal(18,2), totalPledgeRecurring decimal(18,2), pledgedValue decimal(18,2));
	CREATE TABLE #tmpContribInvoices (contributionID int, invoiceID int, amountDue decimal(18,2));
	CREATE TABLE #tmpContribInvoiceProfiles (programID int, invoiceProfileID int, invoiceProfilesCount int);
	CREATE TABLE #tmpContribProgramPayProfiles (programID int, profileID int);
	CREATE TABLE #tmpContribProgramPayProfileIDs (programID int, profileIDList varchar(200));

	INSERT INTO #tmpContributions (programID, programName, rateName, frequency, contributionID, isPerpetual,
		startdate, endDate, statusName, statusCode)
	select p.programID, p.programName, cr.rateName, f.frequency, c.contributionID, c.isPerpetual,
		c.startdate, c.endDate, cps.statusName, cps.statusCode
	from dbo.sites as s
	inner join dbo.cms_applicationInstances as ai on ai.siteID = s.siteID
	inner join dbo.cp_programs as p on p.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cp_contributions as c on c.programID = p.programID
	inner join dbo.cp_statuses as cps on cps.statusID = c.statusID
		and cps.statusCode in ('P','A','Q')
	inner join dbo.cp_frequencies as f on f.frequencyID = c.frequencyID
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = c.memberID
		and m.activeMemberID = @memberID
	left outer join dbo.cp_rates as cr on cr.rateID = c.rateID
	where s.siteID = @siteID;

	-- get pledged amount
	SELECT @contributionIDList = COALESCE(@contributionIDList + ',','') + cast(contributionID as varchar(10)) from #tmpContributions;
	INSERT INTO #tmpContributionsPledged (contributionID, totalPledgeFirst, totalPledgeRecurring, pledgedValue)
	EXEC dbo.cp_getContributionAmountSplit @contributionIDList=@contributionIDList;

	UPDATE tmp
	set tmp.firstPaymentDate = tmpFP.firstPaymentDate,
		tmp.payProfileID = tmpCC.payProfileID,
		tmp.payProcessFee = tmpCC.payProcessFee,
		tmp.processFeePercent = tmpCC.processFeePercent,
		tmp.totalPledgeFirst = ple.totalPledgeFirst,
		tmp.totalPledgeRecurring = ple.totalPledgeRecurring
	from #tmpContributions as tmp
	inner join (
		select cs.contributionID, min(cs.dueDate) as firstPaymentDate
		from dbo.cp_contributionSchedule as cs
		inner join #tmpContributions as tmp1 on tmp1.contributionID = cs.contributionID
		group by cs.contributionID
	) as tmpFP on tmpFP.contributionID = tmp.contributionID
	inner join (
		select tmp1.contributionID, max(isnull(cpp.payProfileID,0)) as payProfileID,
			max(cast(isnull(cpp.payProcessFee,0) as int)) as payProcessFee, max(isnull(cpp.processFeePercent,0)) as processFeePercent
		from #tmpContributions as tmp1
		left outer join dbo.cp_contributionPayProfiles as cpp on cpp.contributionID = tmp1.contributionID
		group by tmp1.contributionID
	) as tmpCC on tmpCC.contributionID = tmp.contributionID
	inner join #tmpContributionsPledged as ple on ple.contributionID = tmp.contributionID;

	INSERT INTO #tmpContribInvoices (contributionID, invoiceID, amountDue)
	select tmp.contributionID, it.invoiceID,
		sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) as amountDue
	from #tmpContributions as tmp
	cross apply dbo.fn_cp_contributionTransactions(tmp.contributionID) as ct
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = ct.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
	where tmp.statusCode <> 'D'
	and invs.[status] in ('Open','Closed','Delinquent')
	group by tmp.contributionID, it.invoiceID
	having sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0;

	-- get the next invoice due date/amount and first overdue invoice date/amount (if present)
	UPDATE tmp
	SET tmp.nextInvDueDate = dueInv.dateDue,
		tmp.nextInvDueAmt = dueInv.amountDue,
		tmp.overDueInvDueDate = overDueInv.dateDue,
		tmp.overDueInvDueAmt = overDueInv.amountDue,
		tmp.totalOverdueAmt = totalOverDue.amountDue,
		tmp.earliestOverdueInvoiceDate = totalOverDue.earliestOverdueInvoiceDate
	FROM #tmpContributions AS tmp
	LEFT OUTER JOIN (
		SELECT tmp.contributionID, i.dateDue, sum(tmp.amountDue) as amountDue,
			ROW_NUMBER() OVER (PARTITION BY tmp.contributionID ORDER BY i.dateDue ASC) AS rowNum
		FROM #tmpContribInvoices AS tmp
		INNER JOIN dbo.tr_invoices AS i ON i.invoiceID = tmp.invoiceID
		WHERE tmp.amountDue > 0
		AND i.dateDue >= @nowDate
		group by tmp.contributionID, i.dateDue
	) AS dueInv 
		ON dueInv.contributionID = tmp.contributionID
		AND dueInv.rowNum = 1
	LEFT OUTER JOIN (
		SELECT tmp.contributionID, i.dateDue, sum(tmp.amountDue) as amountDue,
			ROW_NUMBER() OVER (PARTITION BY tmp.contributionID ORDER BY i.dateDue ASC) AS rowNum
		FROM #tmpContribInvoices AS tmp
		INNER JOIN dbo.tr_invoices AS i ON i.invoiceID = tmp.invoiceID
		WHERE tmp.amountDue > 0
		AND i.dateDue < @nowDate
		group by tmp.contributionID, i.dateDue
	) AS overDueInv 
		ON overDueInv.contributionID = tmp.contributionID
		AND overDueInv.rowNum = 1
	LEFT OUTER JOIN (
		SELECT tmp.contributionID, sum(tmp.amountDue) AS amountDue, min(i.dateDue) as earliestOverdueInvoiceDate 
		FROM #tmpContribInvoices AS tmp
		INNER JOIN dbo.tr_invoices AS i ON i.invoiceID = tmp.invoiceID
		WHERE tmp.amountDue > 0
		AND i.dateDue < @nowDate
		group by tmp.contributionID
	) AS totalOverDue 
		ON totalOverDue.contributionID = tmp.contributionID;

	-- get the next installment due date & amount (if available)
	UPDATE tmp
	SET tmp.nextInstallmentDueDate = inst.dueDate,
		tmp.nextInstallmentDueAmt = inst.amount
	FROM #tmpContributions AS tmp
	INNER JOIN (
		SELECT tmp.contributionID, ti.dueDate, t.amount,
			ROW_NUMBER() OVER (PARTITION BY tmp.contributionID ORDER BY ti.dueDate ASC) AS rowNum
		FROM dbo.tr_transactionInstallments AS ti
		INNER JOIN dbo.cp_contributionSchedule AS cs ON cs.scheduleID = ti.scheduleID
		INNER JOIN #tmpContributions AS tmp ON tmp.contributionID = cs.contributionID
		INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = ti.orgID and t.transactionID = ti.transactionID
		WHERE ti.isActive = 1
		AND ti.isConverted = 0
		AND ti.isPaidOnCreate = 0
		AND t.statusID = 1
		AND t.amount > 0
		AND ti.dueDate >= @nowDate
	) AS inst ON inst.contributionID = tmp.contributionID
	WHERE inst.rowNum = 1;

	-- contribution program invoice profiles
	INSERT INTO #tmpContribInvoiceProfiles (programID, invoiceProfileID)
	SELECT tmp.programID, ip.profileID
	FROM #tmpContributions AS tmp
	INNER JOIN dbo.cp_distributions AS cpd ON cpd.programID = tmp.programID
	INNER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = cpd.GLAccountID
	INNER JOIN dbo.tr_invoiceProfiles AS ip ON ip.orgID = @orgID AND ip.profileID = gl.invoiceProfileID
		UNION
	SELECT tmp.programID, ip.profileID
	FROM #tmpContributions AS tmp
	INNER JOIN dbo.cp_programs AS cp ON cp.programID = tmp.programID
	INNER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = cp.defaultGLAccountID
	INNER JOIN dbo.tr_invoiceProfiles AS ip ON ip.orgID = @orgID AND ip.profileID = gl.invoiceProfileID;

	-- contribution program qualified payment profiles
	INSERT INTO #tmpContribProgramPayProfiles (programID, profileID)
	SELECT tmp.programID, p.profileID
	FROM #tmpContribInvoiceProfiles AS tmp
	INNER JOIN dbo.tr_invoiceProfiles AS ip ON ip.orgID = @orgID AND ip.profileID = tmp.invoiceProfileID
	INNER JOIN dbo.tr_invoiceProfilesMerchantProfiles AS invP ON invP.invoiceProfileID = ip.profileID
	INNER JOIN dbo.mp_profiles AS p ON p.profileID = invP.merchantProfileID
	INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = p.gatewayID
	WHERE p.[status] = 'A'
	AND g.isActive = 1
	AND g.gatewayID NOT IN (2,13,14)
	GROUP BY tmp.programID, p.profileID;

	INSERT INTO #tmpContribProgramPayProfileIDs (programID, profileIDList)
	SELECT programID, STRING_AGG(profileID,',')
	FROM #tmpContribProgramPayProfiles
	GROUP BY programID;

	UPDATE c
	SET c.eligibleProfileIDList = tmp.profileIDList
	FROM #tmpContributions AS c
	INNER JOIN #tmpContribProgramPayProfileIDs AS tmp ON tmp.programID = c.programID;

	-- get override processing fee setting
	WITH selectedForFeeOverride AS (
		SELECT programID
		FROM #tmpContribInvoiceProfiles
		GROUP BY programID
		HAVING COUNT(invoiceProfileID) = 1
	)
	INSERT INTO #tmpInvoiceProfileProcFeeOverrides (refID, enableProcessingFeeDonation, processFeeDonationFETitle, processFeeDonationFEMsg, processFeeDonationDefaultSelect)
	SELECT tmp.programID, ip.enableProcessingFeeDonation, pfm.title, pfm.[message], ip.processFeeDonationDefaultSelect
	FROM #tmpContribInvoiceProfiles AS tmp
	INNER JOIN selectedForFeeOverride AS sel ON sel.programID = tmp.programID
	INNER JOIN dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID
		AND ip.profileID = tmp.invoiceProfileID
	LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = ip.solicitationMessageID;

	UPDATE tmp
	SET tmp.ovEnableProcessingFeeDonation = tmpOv.enableProcessingFeeDonation,
		tmp.ovProcessFeeDonationFETitle = tmpOv.processFeeDonationFETitle,
		tmp.ovProcessFeeDonationFEMsg = tmpOv.processFeeDonationFEMsg,
		tmp.ovProcessFeeDonationDefaultSelect = tmpOv.processFeeDonationDefaultSelect
	FROM #tmpContributions as tmp
	LEFT OUTER JOIN #tmpInvoiceProfileProcFeeOverrides AS tmpOv ON tmpOv.refID = tmp.programID;

	TRUNCATE TABLE #tmpInvoiceProfileProcFeeOverrides;

	IF OBJECT_ID('tempdb..#tmpContributionsPledged') IS NOT NULL
		DROP TABLE #tmpContributionsPledged;
	IF OBJECT_ID('tempdb..#tmpContribInvoices') IS NOT NULL
		DROP TABLE #tmpContribInvoices;
	IF OBJECT_ID('tempdb..#tmpContribProgramPayProfiles') IS NOT NULL
		DROP TABLE #tmpContribProgramPayProfiles;
	IF OBJECT_ID('tempdb..#tmpContribProgramPayProfiles') IS NOT NULL
		DROP TABLE #tmpContribProgramPayProfiles;
	IF OBJECT_ID('tempdb..#tmpContribProgramPayProfileIDs') IS NOT NULL
		DROP TABLE #tmpContribProgramPayProfileIDs;

	-- ad hoc invoices
	IF OBJECT_ID('tempdb..#tmpInv') IS NOT NULL
		DROP TABLE #tmpInv;
	IF OBJECT_ID('tempdb..#tmpInvExclude') IS NOT NULL
		DROP TABLE #tmpInvExclude;
	IF OBJECT_ID('tempdb..#tmpInvDetails') IS NOT NULL
		DROP TABLE #tmpInvDetails;
	IF OBJECT_ID('tempdb..#tmpInvPPCount') IS NOT NULL
		DROP TABLE #tmpInvPPCount;
	IF OBJECT_ID('tempdb..#tmpFinalInvoices') IS NOT NULL
		DROP TABLE #tmpFinalInvoices;
	CREATE TABLE #tmpInv (invoiceID int PRIMARY KEY);
	CREATE TABLE #tmpInvExclude (invoiceID int PRIMARY KEY);
	CREATE TABLE #tmpInvDetails (invoiceID int PRIMARY KEY, invoiceNumber varchar(18), invoiceCode char(8), dueDate datetime, payProfileID int, payProcessFee bit,
		processFeePercent decimal(5, 2), eligibleProfileIDList varchar(max));
	CREATE TABLE #tmpInvPPCount (invoiceID int PRIMARY KEY, totalCount int, eligibleCount int, eligibleProfileIDList varchar(max));
	CREATE TABLE #tmpFinalInvoices (invoiceID int PRIMARY KEY, invoiceNumber varchar(18), invoiceCode char(8), payProfileID int,
		payProcessFee bit, processFeePercent decimal(5, 2), dueDate datetime, dueAmt decimal(14,2), eligibleProfileIDList varchar(max),
		ovEnableProcessingFeeDonation bit, ovProcessFeeDonationFETitle varchar(100), ovProcessFeeDonationFEMsg varchar(800), ovProcessFeeDonationDefaultSelect bit);

	DECLARE @orgCode varchar(10);

	SELECT @orgCode = orgCode FROM dbo.organizations WHERE orgID = @orgID;

	INSERT INTO #tmpInv (invoiceID)
	EXEC dbo.tr_getInvoicesAssociatedToMember @memberID=@memberID;

	INSERT INTO #tmpInvExclude(invoiceID)
	SELECT DISTINCT i.invoiceID
	FROM #tmpInv as i
	INNER JOIN dbo.tr_invoiceItems as tri on tri.orgID = @orgID
		AND tri.invoiceID = i.invoiceID;

	DELETE tmp
	FROM #tmpInv AS tmp
	WHERE EXISTS (SELECT 1 FROM #tmpInvExclude WHERE invoiceID = tmp.invoiceID);
	
	INSERT INTO #tmpInvPPCount(invoiceID, totalCount, eligibleCount, eligibleProfileIDList)
	SELECT tmp.invoiceID, total.totalCount, ISNULL(eligible.eligibleCount,0), eligible.profileIDList
	FROM #tmpInv AS tmp
	INNER JOIN (
		SELECT tmp.invoiceID, COUNT(imp.impID) totalCount
		FROM #tmpInv AS tmp
		LEFT OUTER JOIN dbo.tr_invoiceMerchantProfiles AS imp ON imp.invoiceID = tmp.invoiceID
		GROUP BY tmp.invoiceID
	) AS total ON total.invoiceID = tmp.invoiceID
	LEFT OUTER JOIN (
		SELECT tmp.invoiceID, COUNT(imp.impID) AS eligibleCount, STRING_AGG(imp.profileID, ',') AS profileIDList
		FROM #tmpInv AS tmp
		INNER JOIN dbo.tr_invoiceMerchantProfiles AS imp ON imp.invoiceID = tmp.invoiceID
		INNER JOIN dbo.mp_profiles AS p ON p.siteID = @siteID
			AND p.profileID = imp.profileID
			AND p.[status] = 'A'
			AND p.allowPayInvoicesOnline = 1
		INNER JOIN mp_gateways AS g ON g.gatewayID = p.gatewayID
			AND g.gatewayClass IN ('creditcard','bankdraft')
		GROUP BY tmp.invoiceID
	) AS eligible ON eligible.invoiceID = tmp.invoiceID;

	INSERT INTO #tmpInvDetails (invoiceID, invoiceNumber, invoiceCode, dueDate, payProfileID, payProcessFee, processFeePercent, eligibleProfileIDList)
	SELECT i.invoiceID, @orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.invoiceCode,
		i.dateDue, i.payProfileID, i.payProcessFee, i.processFeePercent, ipp.eligibleProfileIDList
	FROM #tmpInv AS tmp
	INNER JOIN dbo.tr_invoices AS i ON i.orgID = @orgID AND i.invoiceID = tmp.invoiceID
	INNER JOIN dbo.tr_invoiceStatuses AS st ON st.statusID = i.statusID
		AND st.[status] IN ('Closed','Delinquent')
	INNER JOIN #tmpInvPPCount AS ipp ON ipp.invoiceID = tmp.invoiceID
	WHERE ipp.totalCount = 0 OR ipp.eligibleCount > 0;

	INSERT INTO #tmpInvoiceProfileProcFeeOverrides (refID, enableProcessingFeeDonation, processFeeDonationFETitle, processFeeDonationFEMsg, processFeeDonationDefaultSelect)
	SELECT tmp.invoiceID, ip.enableProcessingFeeDonation, pfm.title, pfm.[message], ip.processFeeDonationDefaultSelect
	FROM #tmpInvDetails AS tmp
	INNER JOIN dbo.tr_invoices AS i ON i.orgID = @orgID AND i.invoiceID = tmp.invoiceID
	INNER JOIN dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID
		AND ip.profileID = i.invoiceProfileID
	LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = ip.solicitationMessageID;

	INSERT INTO #tmpFinalInvoices (invoiceID, invoiceNumber, invoiceCode, payProfileID, payProcessFee, processFeePercent, dueDate, dueAmt, eligibleProfileIDList)
	select tmp.invoiceID, tmp.invoiceNumber, tmp.invoiceCode, tmp.payProfileID, tmp.payProcessFee, tmp.processFeePercent, tmp.dueDate,
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as dueAmt,
		tmp.eligibleProfileIDList
	from #tmpInvDetails as tmp
	left outer join dbo.tr_invoiceTransactions as it
	on it.orgID = @orgID and it.invoiceID = tmp.invoiceID
	group by tmp.invoiceID, tmp.invoiceNumber, tmp.invoiceCode, tmp.payProfileID, tmp.payProcessFee, tmp.processFeePercent, tmp.dueDate, tmp.eligibleProfileIDList
	having tmp.invoiceID = tmp.invoiceID;

	UPDATE tmp
	SET tmp.ovEnableProcessingFeeDonation = tmpOv.enableProcessingFeeDonation,
		tmp.ovProcessFeeDonationFETitle = tmpOv.processFeeDonationFETitle,
		tmp.ovProcessFeeDonationFEMsg = tmpOv.processFeeDonationFEMsg,
		tmp.ovProcessFeeDonationDefaultSelect = tmpOv.processFeeDonationDefaultSelect
	FROM #tmpFinalInvoices as tmp
	LEFT OUTER JOIN #tmpInvoiceProfileProcFeeOverrides AS tmpOv ON tmpOv.refID = tmp.invoiceID;

	TRUNCATE TABLE #tmpInvoiceProfileProcFeeOverrides;

	IF OBJECT_ID('tempdb..#tmpInv') IS NOT NULL
		DROP TABLE #tmpInv;
	IF OBJECT_ID('tempdb..#tmpInvExclude') IS NOT NULL
		DROP TABLE #tmpInvExclude;
	IF OBJECT_ID('tempdb..#tmpInvDetails') IS NOT NULL
		DROP TABLE #tmpInvDetails;
	IF OBJECT_ID('tempdb..#tmpInvPPCount') IS NOT NULL
		DROP TABLE #tmpInvPPCount;

	-- get display order for all entries based on due dates
	IF OBJECT_ID('tempdb..#tmpSortOrder') IS NOT NULL
		DROP TABLE #tmpSortOrder;
	CREATE TABLE #tmpSortOrder (rowNum int PRIMARY KEY IDENTITY(1,1), [typeOrder] int, referenceID int, sortString varchar(max), dueDate datetime, dispOrder int);

	INSERT INTO #tmpSortOrder ([typeOrder], referenceID, sortString, dueDate)
	SELECT 1, subscriberID, subscriptionName + rateName, ISNULL(overDueInvDueDate,nextInvDueDate)
	FROM #tmpSubs
		UNION
	SELECT 2, contributionID, programName + rateName, ISNULL(overDueInvDueDate,nextInvDueDate)
	FROM #tmpContributions
		UNION
	SELECT 3, invoiceID, invoiceNumber, dueDate
	FROM #tmpFinalInvoices;

	WITH entriesOrdered AS (
		SELECT rowNum,
			ROW_NUMBER() OVER (ORDER BY CASE WHEN dueDate IS NULL THEN 1 ELSE 0 END ASC, dueDate ASC, typeOrder ASC, sortString ASC) AS dispOrder
		FROM #tmpSortOrder
	)
	UPDATE tmp
	SET tmp.dispOrder = ord.dispOrder
	FROM #tmpSortOrder AS tmp
	INNER JOIN entriesOrdered AS ord ON ord.rowNum = tmp.rowNum;

	-- subscriptions
	SELECT tmp.subscriberID, tmp.subscriptionID, tmp.typeName, tmp.subscriptionName, tmp.[status], tmp.statusName, tmp.subStartDate, tmp.subEndDate,
		tmp.payProfileID, tmp.payProcessFee, tmp.processFeePercent, tmp.rateName, tmp.billedAmt, tmp.dueAmt, tmp.nextInvDueDate,
		tmp.nextInvDueAmt, tmp.overDueInvDueDate, tmp.overDueInvDueAmt, tmp.earliestOverdueInvoiceDate, tmp.totalOverdueAmt, tmp.eligibleProfileIDList, o.dispOrder,
		tmp.ovEnableProcessingFeeDonation, tmp.ovProcessFeeDonationFETitle, tmp.ovProcessFeeDonationFEMsg, tmp.ovProcessFeeDonationDefaultSelect
	FROM #tmpSubs AS tmp
	INNER JOIN #tmpSortOrder AS o ON o.typeOrder = 1
		AND o.referenceID = tmp.subscriberID
	ORDER BY tmp.nextInvDueDate DESC, tmp.subscriptionName;

	-- contributions
	SELECT tmp.contributionID, tmp.programID, tmp.programName, tmp.rateName, tmp.statusName, tmp.frequency,
		tmp.isPerpetual, tmp.startdate, tmp.endDate, tmp.totalPledgeFirst, tmp.totalPledgeRecurring, tmp.firstPaymentDate,
		NULLIF(tmp.payProfileID,0) AS payProfileID, tmp.payProcessFee, tmp.processFeePercent, tmp.nextInvDueDate, tmp.nextInvDueAmt, tmp.nextInstallmentDueDate,
		tmp.nextInstallmentDueAmt, tmp.overDueInvDueDate, tmp.overDueInvDueAmt, tmp.earliestOverdueInvoiceDate, tmp.totalOverdueAmt, tmp.eligibleProfileIDList, o.dispOrder,
		tmp.ovEnableProcessingFeeDonation, tmp.ovProcessFeeDonationFETitle, tmp.ovProcessFeeDonationFEMsg, tmp.ovProcessFeeDonationDefaultSelect
	FROM #tmpContributions AS tmp
	INNER JOIN #tmpSortOrder AS o ON o.typeOrder = 2
		AND o.referenceID = tmp.contributionID
	ORDER BY tmp.nextInvDueDate, tmp.programName;

	-- adhoc invoices
	SELECT tmp.invoiceID, tmp.invoiceNumber, tmp.invoiceCode, tmp.payProfileID, tmp.payProcessFee, tmp.processFeePercent,
		tmp.dueDate, tmp.dueAmt, CASE WHEN tmp.dueDate < @nowDate THEN 1 ELSE 0 END AS isOverDue, tmp.eligibleProfileIDList, o.dispOrder,
		tmp.ovEnableProcessingFeeDonation, tmp.ovProcessFeeDonationFETitle, tmp.ovProcessFeeDonationFEMsg, tmp.ovProcessFeeDonationDefaultSelect
	FROM #tmpFinalInvoices AS tmp
	INNER JOIN #tmpSortOrder AS o ON o.typeOrder = 3
		AND o.referenceID = tmp.invoiceID;

	IF OBJECT_ID('tempdb..#tblSubscription') IS NOT NULL
		DROP TABLE #tblSubscription;
	IF OBJECT_ID('tempdb..#tblSubscriptionRate') IS NOT NULL
		DROP TABLE #tblSubscriptionRate;
	IF OBJECT_ID('tempdb..#tmpSubs') IS NOT NULL
		DROP TABLE #tmpSubs;
	IF OBJECT_ID('tempdb..#tmpSubsExistingInvoices') IS NOT NULL
		DROP TABLE #tmpSubsExistingInvoices;
	IF OBJECT_ID('tempdb..#mcSubscribersForAcct') IS NOT NULL
		DROP TABLE #mcSubscribersForAcct;
	IF OBJECT_ID('tempdb..#mcSubscriberTransactions') IS NOT NULL
		DROP TABLE #mcSubscriberTransactions;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfileProcFeeOverrides') IS NOT NULL
		DROP TABLE #tmpInvoiceProfileProcFeeOverrides;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO