<cfsavecontent variable="local.chargeBackTransJS">
	<cfoutput>
	<cfif local.qryInvoices.recordcount>
		<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/selectBox/jquery.sb.min.js"></script>
		<link rel="stylesheet" href="/assets/common/javascript/jQueryAddons/selectBox/jquery.sb.css" type="text/css" media="all" />
	</cfif>
	<script language="javascript">
	function hideAlert() { mca_hideAlert('err_chargeback_trans'); };
	function selectGLAccount() {
		$('##divChargebackForm').hide();
		toggleGLASelectorGridArea(true);
	}
	function selectGLAccountResult(objGL) {
		if (objGL.thepathexpanded.length > 0) {
			$('##GLAccountPath').html('<span class="mr-2">' + objGL.thepathexpanded + '</span> (<span class="text-danger font-weight-bold">Remember to save!</span>)');
			$('##GLAccountID').val(objGL.glaccountid);
		} else {
			var msg = 'There was a problem selecting the Revenue Account for this fee.<br/>
				Try again; if the issue persists, contact MemberCentral for assistance.';
			$('##divGLerr').html(msg).show();
		}
		$('##divChargebackForm').show();
		toggleGLASelectorGridArea(false,true);
	}
	function checkChargebackForm() {
		top.$('##btnMCModalSave').prop('disabled',true);
		hideAlert();
		var arrReq = new Array();

		$('##nsfAmount').val(formatCurrency($('##nsfAmount').val()));
		$('##fee').val(formatCurrency($('##fee').val()));

		if (parseFloat($('##nsfAmount').val()) < 0) arrReq[arrReq.length] = 'You may not enter a negative #local.NSFWord# amount.';
		else if (parseFloat($('##nsfAmount').val()) == 0) arrReq[arrReq.length] = 'You may not enter 0 as the #local.NSFWord# amount.';
		else if (parseFloat($('##nsfAmount').val()) > #local.qryTransaction.maxNSFAmount#) arrReq[arrReq.length] = 'This payment only has #dollarformat(local.qryTransaction.maxNSFAmount)# in refundable funds. The #local.NSFWord# amount may not exceed this refundable amount.';

		var feerdo = $('input[name=rdoFee]:checked','##frmChargeback').val();
		if (feerdo == 1) {
			if (parseFloat($('##fee').val()) < 0) arrReq[arrReq.length] = 'You may not enter a negative fee amount.';
			else if (parseFloat($('##fee').val()) == 0) arrReq[arrReq.length] = 'You may not enter 0 as the fee amount.';
			if ($('##GLAccountID').val() == 0 || $('##GLAccountID').val().length == 0) arrReq[arrReq.length] = 'Choose the Revenue Account for this fee.';

			let stateIDforTax = $('##stateIDforTax').val();
			let zipForTax = $('##zipForTax').val();
			if (stateIDforTax.length == 0 || stateIDforTax == 0) arrReq[arrReq.length] = 'Select a State/Province for the missing tax information.';
			if (zipForTax.length == 0) arrReq[arrReq.length] = 'Enter a Postal Code for the missing tax information.';
			if (stateIDforTax > 0 && zipForTax.length && !mc_isValidBillingZip(zipForTax,stateIDforTax,''))
				arrReq[arrReq.length] = 'Invalid Postal Code.';
		}

		if (arrReq.length) {
			mca_showAlert('err_chargeback_trans', arrReq.join('<br/>'));
			top.$('##btnMCModalSave').prop('disabled',false);
			return false;
		}

		window.setTimeout(() => { top.$('##btnMCModalSave').remove(); }, 1500);
		return true;
	}
	function chooseFee(f) {
		$('##feeDIV').toggleClass('d-none', !f);
		if (!f) $('##fee').val('0.00');
		hideAlert();
	}

	$(function() {
		mca_setupDatePickerField('batchDate', '#dateformat(local.qryTransaction.transactionDate,'m/d/yyyy')#', '#dateformat(now(),'m/d/yyyy')#');
		mca_setupCalendarIcons('frmChargeback');

		<cfif local.qryInvoices.recordcount>
			$('##invoiceID').sb( {
				fixedWidth: true,
				optionFormat: function() { return $(this).attr("sbt"); },
				displayFormat: function() { return $(this).text(); }
			} );
		</cfif>

		top.$('##MCModalLabel').html('#encodeForJavaScript("#local.NSFWord# Confirmation Needed")#');
		top.MCModalUtils.buildFooter({
			classlist: 'd-flex justify-content-between',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmChargeback :submit").click',
			extrabuttonlabel: '#encodeForJavaScript("Save #local.NSFWord#")#'
		});
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.chargeBackTransJS)#">

<cfoutput>
<div id="divChargebackForm" class="p-3">
	<form name="frmChargeback" id="frmChargeback" action="#local.formlink#" method="post" onsubmit="return checkChargebackForm();">
	<input type="hidden" name="tid" id="tid" value="#local.qryTransaction.transactionID#">
	<input type="hidden" name="GLAccountID" id="GLAccountID" value="0">

	<!--- hidden submit triggered from parent --->
	<button type="submit" class="d-none"></button>
	
	<div id="err_chargeback_trans" class="alert alert-danger mb-2 d-none"></div>

	<table class="table table-sm table-borderless table-striped table-hover border mb-3">
		<thead class="border">
			<tr>
				<th width="15%">Date</th>
				<th>Amt</th>
				<th>Detail</th>
			</tr>
		</thead>
		<tbody>
		<cfloop query="local.qryTransaction">
			<tr>
				<td>#dateformat(local.qryTransaction.transactionDate,'m/d/yyyy')#</td>
				<td class="pl-2">#dollarFormat(local.qryTransaction.amount)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif></td>
				<td class="pl-2">#local.qryTransaction.detail#</td>
			</tr>
			<cfif local.qryTransaction.maxNSFAmount is not local.qryTransaction.amount>
				<tr>
					<td>&nbsp;</td>
					<td class="text-danger pl-2">#dollarFormat(local.qryTransaction.maxNSFAmount)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif></td>
					<td class="text-danger pl-2">Maximum refundable amount</td>
				</tr>
			</cfif>
		</cfloop>
		</tbody>
	</table>

	<div class="mb-1"><b>#local.NSFWord# From Bank</b></div>
	<div class="ml-3 mb-3">
		<table class="table table-sm table-borderless">
		<tr>
			<td class="align-top text-nowrap">Date Posted to Bank:</td>
			<td width="10">&nbsp;</td>
			<td class="align-top">
				<div class="input-group input-group-sm" style="width:160px;">
					<input type="text" name="batchDate" id="batchDate" value="#dateformat(now(),'m/d/yyyy')#" class="form-control form-control-sm dateControl" autocomplete="off" onclick="hideAlert();">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="batchDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
			</td>
			<td width="10" rowspan="2">&nbsp;</td>
			<td class="align-top" rowspan="2">
				<i>We'll put this on a chargeback batch using the chosen date as the batch's deposit date.</i>
			</td>
		</tr>
		<tr>
			<td class="align-top">Amount of #local.NSFWord#:</td>
			<td class="align-top" width="10">&nbsp;</td>
			<td class="align-top">
				<div class="d-flex align-items-center" style="width:160px;">
					<div class="input-group input-group-sm">
						<div class="input-group-prepend"><span class="input-group-text">$</span></div>
						<input type="text" name="nsfAmount" id="nsfAmount" value="#numberformat(local.qryTransaction.maxNSFAmount,"0.00")#" class="form-control form-control-sm" autocomplete="off" onblur="this.value=formatCurrency(this.value);hideAlert();">
					</div>
					<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1><span class="ml-1">#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</span></cfif>
				</div>
			</td>
		</tr>
		</table>
	</div>
	
	<div class="mb-1">
		<span class="font-weight-bold mr-2">Would you like to also pass along a fee to this member?</span>
		<div class="form-check-inline">
			<input type="radio" name="rdoFee" id="rdoFee_1" value="1" class="form-check-input" onclick="chooseFee(true);">
			<label for="rdoFee_1" class="form-check-label">Yes</label>
		</div>
		<div class="form-check-inline">
			<input type="radio" name="rdoFee" id="rdoFee_0" value="0" class="form-check-input" onclick="chooseFee(false);" checked>
			<label for="rdoFee_0" class="form-check-label">No</label>
		</div>
	</div>
	<div id="feeDIV" class="ml-3 d-none">
		<table class="table table-sm table-borderless">
		<tr>
			<td class="align-top">Fee Amount:</td>
			<td width="10">&nbsp;</td>
			<td class="align-top">
				<div class="d-flex align-items-center" style="width:200px;">
					<div class="input-group input-group-sm">
						<div class="input-group-prepend"><span class="input-group-text">$</span></div>
						<input type="text" name="fee" id="fee" value="0.00" class="form-control form-control-sm" autocomplete="off" onblur="this.value=formatCurrency(this.value);hideAlert();">
					</div>
					<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1><span class="ml-1">#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</span></cfif>
				</div>
			</td>
		</tr>
		<tr><td colspan="3"></td></tr>

		<!--- prompt for missing tax information --->
		<cfif NOT local.stateIDforTax gt 0 OR NOT len(local.zipForTax)>
			<tr>
				<td class="align-top">
					Tax Information Required:<br/>
					<small>Assignee's billing address is missing this information.</small>
				</td>
				<td width="10">&nbsp;</td>
				<td class="align-top">
					<div class="pb-2">
						<cfset local.qryStates = application.objCommon.getStates()>
						<select id="stateIDforTax" name="stateIDforTax" class="form-control form-control-sm">
							<option value="">Select State/Province</option>
							<cfset local.currentCountryID = 0>
							<cfloop query="local.qryStates">
								<cfif local.qryStates.countryID neq local.currentCountryID>
									<cfset local.currentCountryID = local.qryStates.countryID>
									<optgroup label="#local.qryStates.country#">
								</cfif>
								<option value="#local.qryStates.stateID#"<cfif val(local.stateIDforTax) eq local.qryStates.stateID> selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
								<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
									</optgroup>
								</cfif>
							</cfloop>
						</select>
					</div>
					<div>
						<input type="text" id="zipForTax" name="zipForTax" value="#local.zipForTax#" class="form-control form-control-sm" placeholder="Enter Postal Code" maxlength="25">
					</div>
				</td>
			</tr>
			<tr><td colspan="3"></td></tr>
		<cfelse>
			<input type="hidden" id="stateIDforTax" name="stateIDforTax" value="#local.stateIDforTax#">
			<input type="hidden" id="zipForTax" name="zipForTax" value="#local.zipForTax#">
		</cfif>

		<tr>
			<td class="align-top">Revenue Account:</td>
			<td width="10">&nbsp;</td>
			<td class="align-top">
				<span id="GLAccountPath">(no account selected)</span>
				<br/><a href="javascript:selectGLAccount();">Choose GL Account</a>
				<div id="divGLerr" class="alert alert-danger" style="display:none;"></div>
			</td>
		</tr>
		<tr><td colspan="3"></td></tr>
		<tr>
			<td class="align-top">Put on Invoice:</td>
			<td width="10">&nbsp;</td>
			<td class="align-top">
				<cfif local.qryInvoices.recordcount>
					<select name="invoiceID" id="invoiceID" class="custom-select-box">
						<option value="0" sbt="<div class='px-2' style='min-width:300px;'>Create a new invoice</div>">Create a new invoice</option>
						<cfloop query="local.qryInvoices">
							<option value="#local.qryInvoices.invoiceID#" sbt="<div class='px-2'><table width='100%'><tr><td width='100'><b>#local.qryInvoices.invoicenumber#</b></td><td width='5'>&nbsp;</td><td width='60' class='text-center'>#dateformat(local.qryInvoices.dateDue,'mm/dd/yyyy')#</td><td width='5'>&nbsp;</td><td width='60' class='text-right'>#dollarformat(local.qryInvoices.invAmt)#</td></tr></table></div>">#local.qryInvoices.invoicenumber#</option>
						</cfloop>
					</select>
				<cfelse>
					<input type="hidden" name="invoiceID" id="invoiceID" value="0">
					A new invoice will be created.
				</cfif>
			</td>
		</tr>
		<tr>
			<td colspan="2"></td>
			<td>
				<div class="form-check-inline">
					<input type="checkbox" name="invoiceAutoClose" id="invoiceAutoClose_1" value="1" class="form-check-input">
					<label for="invoiceAutoClose_1" class="form-check-label">Close invoice after recording fee.</label>
				</div>
			</td>
		</tr>
		</table>
	</div>
	</form>
</div>
#local.showGLSelector.data#
</cfoutput>